import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';

export default defineConfig({
  plugins: [
    monkey({
      entry: 'src/main.dev.ts',
      userscript: {
        name: 'xueqiu enhancer',
        namespace: 'https://github.com/virtual2016/xueqiu-enhancer',
        version: '1.0.0-dev',
        description: '解决雪球页面抖动等问题的增强脚本 (开发版)',
        author: 'virtual2016',
        match: ['https://xueqiu.com/*'],
        icon: 'https://xueqiu.com/favicon.ico',
        grant: ['none'],
        license: 'MIT',
      },
      build: {
        externalGlobals: {},
      },
      server: {
        mountGM: false,
        open: false,
      },
    }),
  ],
  build: {
    target: 'es2020',
    minify: false,
    sourcemap: false,
    rollupOptions: {
      output: {
        format: 'iife',
      },
    },
  },
  define: {
    'process.env.NODE_ENV': '"development"',
  },
  server: {
    port: 3000,
    open: false,
  },
});
