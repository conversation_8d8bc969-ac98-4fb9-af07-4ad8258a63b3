import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';

export default defineConfig({
  plugins: [
    monkey({
      entry: 'src/main.ts',
      userscript: {
        name: 'xueqiu enhancer',
        namespace: 'https://github.com/virtual2016/xueqiu-enhancer',
        version: '1.0.0',
        description: '解决雪球页面抖动等问题的增强脚本',
        author: 'virtual2016',
        match: ['https://xueqiu.com/*'],
        icon: 'https://xueqiu.com/favicon.ico',
        grant: ['none'],
        license: 'MIT',
        homepage: 'https://github.com/virtual2016/xueqiu-enhancer',
        supportURL: 'https://github.com/virtual2016/xueqiu-enhancer/issues',
        updateURL: 'https://github.com/virtual2016/xueqiu-enhancer/raw/main/dist/xueqiu-enhancer.user.js',
        downloadURL: 'https://github.com/virtual2016/xueqiu-enhancer/raw/main/dist/xueqiu-enhancer.user.js',
      },
      build: {
        externalGlobals: {},
      },
      server: {
        mountGM: false,
      },
    }),
  ],
  build: {
    target: 'es2020',
    minify: true,
    sourcemap: false,
    rollupOptions: {
      output: {
        format: 'iife',
      },
    },
  },
  define: {
    __DEV__: false,
  },
});
