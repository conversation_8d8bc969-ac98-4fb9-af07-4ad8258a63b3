/**
 * 日志工具类
 */
export class Logger {
  private prefix: string;

  constructor(prefix: string = 'XueqiuEnhancer') {
    this.prefix = `[${prefix}]`;
  }

  /**
   * 输出信息日志
   */
  public info(message: string, ...args: any[]): void {
    console.log(`${this.prefix} ${message}`, ...args);
  }

  /**
   * 输出警告日志
   */
  public warn(message: string, ...args: any[]): void {
    console.warn(`${this.prefix} ${message}`, ...args);
  }

  /**
   * 输出错误日志
   */
  public error(message: string, ...args: any[]): void {
    console.error(`${this.prefix} ${message}`, ...args);
  }

  /**
   * 输出调试日志
   */
  public debug(message: string, ...args: any[]): void {
    // 在开发环境下输出调试日志
    console.debug(`${this.prefix} ${message}`, ...args);
  }
}
