# 雪球增强器 (<PERSON><PERSON><PERSON><PERSON> Enhancer)

一个用于增强雪球网站用户体验的油猴脚本，主要解决页面抖动等问题。

## 功能特性

- ✅ **页面抖动修复**: 解决弹窗导致的滚动条消失/出现引起的页面抖动问题
- 🔧 **模块化设计**: 采用 TypeScript + Vite 构建，代码结构清晰
- 🎯 **智能检测**: 自动检测弹窗状态，动态调整页面布局
- 🚀 **性能优化**: 使用 MutationObserver 高效监听 DOM 变化

## 安装使用

### 方法一：直接安装（推荐）

1. 安装 [Tampermonkey](https://www.tampermonkey.net/) 浏览器扩展
2. 点击 [安装脚本](https://github.com/virtual2016/xueqiu-enhancer/raw/main/dist/xueqiu-enhancer.user.js)
3. 访问 [雪球网站](https://xueqiu.com) 即可自动生效

### 方法二：本地开发

```bash
# 克隆项目
git clone https://github.com/virtual2016/xueqiu-enhancer.git
cd xueqiu-enhancer

# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build
```

## 技术栈

- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 快速的构建工具
- **vite-plugin-monkey**: 油猴脚本开发插件

## 项目结构

```
src/
├── main.ts              # 主入口文件
├── modules/             # 功能模块
│   └── scrollbar-fixer.ts  # 滚动条修复器
├── utils/               # 工具函数
│   ├── logger.ts        # 日志工具
│   └── dom-utils.ts     # DOM 操作工具
└── styles/              # 样式文件
    └── main.css         # 主样式
```

## 问题解决

### 页面抖动问题

雪球网站在打开弹窗时会隐藏页面滚动条，关闭弹窗时又会显示滚动条，这导致页面内容发生水平位移，产生抖动效果。

**解决方案**：
1. 监听弹窗的打开/关闭状态
2. 在弹窗打开时，为 body 添加右边距来补偿滚动条宽度
3. 在弹窗关闭时，移除补偿边距

## 开发说明

### 添加新功能

1. 在 `src/modules/` 目录下创建新的功能模块
2. 在 `src/main.ts` 中引入并初始化新模块
3. 如需样式，在 `src/styles/` 目录下添加对应的 CSS 文件

### 调试技巧

- 开发模式下会输出详细的调试日志
- 使用浏览器开发者工具查看控制台输出
- 可以通过修改 Logger 类来调整日志级别

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
