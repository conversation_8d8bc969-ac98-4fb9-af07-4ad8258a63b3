{"name": "xueqiu-enhancer", "version": "1.0.0", "description": "雪球增强器 - 解决页面抖动等问题的油猴脚本", "type": "module", "scripts": {"dev": "node scripts/dev-watch.js", "dev:server": "vite --config vite.config.dev.ts", "dev:browser": "node scripts/open-dev-browser.js", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "dev:watch": "vite build --watch --config vite.config.dev.ts"}, "keywords": ["userscript", "tampermonkey", "<PERSON><PERSON><PERSON><PERSON>", "enhancer"], "author": "virtual2016", "license": "MIT", "devDependencies": {"@types/tampermonkey": "^5.0.4", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-monkey": "^5.0.9"}}