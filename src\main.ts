/**
 * 雪球增强器主入口文件
 * 解决页面抖动等问题
 */

import { ScrollbarFixer } from './modules/scrollbar-fixer';
import { Logger } from './utils/logger';
import './styles/main.css';

class XueqiuEnhancer {
  private scrollbarFixer: ScrollbarFixer;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('XueqiuEnhancer');
    this.scrollbarFixer = new ScrollbarFixer();
  }

  /**
   * 初始化增强器
   */
  public init(): void {
    this.logger.info('雪球增强器启动中...');
    
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * 启动所有功能模块
   */
  private start(): void {
    try {
      // 初始化滚动条修复器
      this.scrollbarFixer.init();
      
      this.logger.info('雪球增强器启动完成');
    } catch (error) {
      this.logger.error('雪球增强器启动失败:', error);
    }
  }
}

// 启动增强器
const enhancer = new XueqiuEnhancer();
enhancer.init();
