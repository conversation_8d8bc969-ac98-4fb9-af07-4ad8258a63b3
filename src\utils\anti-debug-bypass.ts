/**
 * 反调试绕过工具
 * 用于绕过雪球网站的开发者工具检测
 */

export class AntiDebugBypass {
  private originalConsole: any = {};
  private originalDebugger: any;

  constructor() {
    this.init();
  }

  /**
   * 初始化反调试绕过
   */
  private init(): void {
    this.bypassDebuggerDetection();
    this.bypassConsoleDetection();
    this.bypassDevToolsDetection();
  }

  /**
   * 绕过 debugger 语句检测
   */
  private bypassDebuggerDetection(): void {
    // 重写 debugger 相关函数
    const noop = () => {};
    
    // 禁用 debugger 语句
    try {
      Object.defineProperty(window, 'debugger', {
        get: () => noop,
        set: () => {},
        configurable: false
      });
    } catch (e) {
      // 忽略错误
    }

    // 重写可能触发调试器的函数
    const originalEval = window.eval;
    window.eval = function(code: string) {
      // 移除 debugger 语句
      const cleanCode = code.replace(/debugger\s*;?/g, '');
      return originalEval.call(this, cleanCode);
    };

    // 重写 Function 构造函数
    const originalFunction = window.Function;
    window.Function = function(...args: any[]) {
      const code = args[args.length - 1];
      if (typeof code === 'string') {
        args[args.length - 1] = code.replace(/debugger\s*;?/g, '');
      }
      return originalFunction.apply(this, args);
    } as any;
  }

  /**
   * 绕过控制台检测
   */
  private bypassConsoleDetection(): void {
    // 保存原始 console 方法
    this.originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // 重写 console 方法以避免检测
    const createConsoleMethod = (originalMethod: Function) => {
      return function(...args: any[]) {
        try {
          return originalMethod.apply(console, args);
        } catch (e) {
          // 静默处理错误
        }
      };
    };

    console.log = createConsoleMethod(this.originalConsole.log);
    console.warn = createConsoleMethod(this.originalConsole.warn);
    console.error = createConsoleMethod(this.originalConsole.error);
    console.info = createConsoleMethod(this.originalConsole.info);
    console.debug = createConsoleMethod(this.originalConsole.debug);
  }

  /**
   * 绕过开发者工具检测
   */
  private bypassDevToolsDetection(): void {
    // 重写可能用于检测开发者工具的属性
    const devtools = {
      open: false,
      orientation: null
    };

    // 阻止开发者工具检测
    setInterval(() => {
      // 重置可能的检测标志
      (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ = undefined;
      (window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ = undefined;
    }, 100);

    // 重写 toString 方法以避免检测
    const originalToString = Function.prototype.toString;
    Function.prototype.toString = function() {
      if (this === console.log || 
          this === console.warn || 
          this === console.error || 
          this === console.info || 
          this === console.debug) {
        return 'function log() { [native code] }';
      }
      return originalToString.call(this);
    };

    // 阻止右键菜单检测
    document.addEventListener('contextmenu', (e) => {
      // 不阻止右键，但标记为已处理
      e.stopImmediatePropagation();
    }, true);

    // 阻止键盘快捷键检测
    document.addEventListener('keydown', (e) => {
      // F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
          (e.ctrlKey && e.key === 'U')) {
        e.stopImmediatePropagation();
      }
    }, true);
  }

  /**
   * 恢复原始状态
   */
  public restore(): void {
    // 恢复 console 方法
    Object.assign(console, this.originalConsole);
  }

  /**
   * 静态方法：快速启用反调试绕过
   */
  public static enable(): AntiDebugBypass {
    return new AntiDebugBypass();
  }
}
