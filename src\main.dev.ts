/**
 * 雪球增强器开发版本
 * 包含反调试绕过功能
 */

import { ScrollbarFixer } from './modules/scrollbar-fixer';
import { Logger } from './utils/logger';
import { AntiDebugBypass } from './utils/anti-debug-bypass';
import './styles/main.css';

class XueqiuEnhancerDev {
  private scrollbarFixer: ScrollbarFixer;
  private logger: Logger;
  private antiDebugBypass: AntiDebugBypass;

  constructor() {
    // 首先启用反调试绕过
    this.antiDebugBypass = AntiDebugBypass.enable();
    
    this.logger = new Logger('XueqiuEnhancer-Dev');
    this.scrollbarFixer = new ScrollbarFixer();
  }

  /**
   * 初始化增强器
   */
  public init(): void {
    this.logger.info('雪球增强器开发版启动中...');
    this.logger.info('已启用反调试绕过功能');
    
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * 启动所有功能模块
   */
  private start(): void {
    try {
      // 初始化滚动条修复器
      this.scrollbarFixer.init();
      
      // 添加开发工具提示
      this.addDevTools();
      
      this.logger.info('雪球增强器开发版启动完成');
    } catch (error) {
      this.logger.error('雪球增强器启动失败:', error);
    }
  }

  /**
   * 添加开发工具
   */
  private addDevTools(): void {
    // 添加开发者工具面板
    const devPanel = document.createElement('div');
    devPanel.id = 'xueqiu-enhancer-dev-panel';
    devPanel.innerHTML = `
      <div style="
        position: fixed;
        top: 10px;
        left: 10px;
        background: #333;
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 999999;
        font-family: monospace;
        max-width: 300px;
      ">
        <div style="font-weight: bold; margin-bottom: 5px;">🔧 雪球增强器 - 开发模式</div>
        <div>状态: <span style="color: #4CAF50;">运行中</span></div>
        <div>反调试: <span style="color: #4CAF50;">已启用</span></div>
        <div>滚动条宽度: <span id="scrollbar-width">检测中...</span></div>
        <div style="margin-top: 5px;">
          <button onclick="this.parentElement.parentElement.style.display='none'" 
                  style="background: #666; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">
            隐藏
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(devPanel);

    // 更新滚动条宽度显示
    setTimeout(() => {
      const scrollbarWidthEl = document.getElementById('scrollbar-width');
      if (scrollbarWidthEl) {
        scrollbarWidthEl.textContent = `${this.scrollbarFixer['scrollbarWidth'] || 0}px`;
      }
    }, 1000);

    // 添加快捷键切换显示
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        const panel = document.getElementById('xueqiu-enhancer-dev-panel');
        if (panel) {
          panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
      }
    });
  }
}

// 启动增强器
const enhancer = new XueqiuEnhancerDev();
enhancer.init();
