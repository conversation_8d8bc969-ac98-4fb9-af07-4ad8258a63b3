/**
 * 雪球增强器样式
 */

/* 确保弹窗过渡动画平滑 */
.modal,
.modals,
.dimmer {
  transition: opacity 0.2s ease-in-out !important;
}

/* 防止弹窗导致的布局跳动 */
body.xq-enhancer-modal-open {
  overflow: hidden !important;
}

/* 优化弹窗背景遮罩 */
.modals.dimmer {
  backdrop-filter: blur(2px);
}

/* 确保弹窗内容居中 */
.modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 优化弹窗关闭按钮 */
.modal .close {
  z-index: 9999 !important;
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
}

/* 防止内容溢出 */
.modal__content,
.modal__bd {
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* 滚动条样式优化 */
.modal__content::-webkit-scrollbar,
.modal__bd::-webkit-scrollbar {
  width: 6px;
}

.modal__content::-webkit-scrollbar-track,
.modal__bd::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal__content::-webkit-scrollbar-thumb,
.modal__bd::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal__content::-webkit-scrollbar-thumb:hover,
.modal__bd::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
