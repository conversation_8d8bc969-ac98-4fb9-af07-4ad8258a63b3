#!/usr/bin/env node

/**
 * 开发模式监听脚本
 * 避免 vite-plugin-monkey 开发模式的调试器问题
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动开发监听模式...');
console.log('📝 文件变化时会自动重新构建');
console.log('📂 构建结果在 dist/ 目录');
console.log('🔄 请手动刷新浏览器页面来加载最新版本');
console.log('');

// 启动 vite build --watch
const buildProcess = spawn('npx', ['vite', 'build', '--watch', '--config', 'vite.config.dev.ts'], {
  stdio: 'inherit',
  shell: true
});

buildProcess.on('error', (error) => {
  console.error('构建进程出错:', error);
});

buildProcess.on('close', (code) => {
  console.log(`构建进程退出，代码: ${code}`);
});

// 监听 Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 停止开发监听...');
  buildProcess.kill();
  process.exit(0);
});
