/**
 * 滚动条修复器
 * 解决弹窗导致的页面抖动问题
 */

import { Logger } from '../utils/logger';
import { DOMUtils } from '../utils/dom-utils';

export class ScrollbarFixer {
  private logger: Logger;
  private scrollbarWidth: number = 0;
  private isModalOpen: boolean = false;
  private originalBodyStyle: string = '';
  private observer: MutationObserver | null = null;

  // 弹窗相关的选择器
  private readonly modalSelectors = [
    '.modal',
    '.modals',
    '.dimmer',
    '.modal__comment',
    '.modal__comment__reply',
    '.modal__login',
    '.modal__pay',
    '.modal__forward',
    '.modal__editor'
  ];

  constructor() {
    this.logger = new Logger('ScrollbarFixer');
  }

  /**
   * 初始化滚动条修复器
   */
  public init(): void {
    this.logger.info('初始化滚动条修复器...');
    
    // 获取滚动条宽度
    this.scrollbarWidth = DOMUtils.getScrollbarWidth();
    this.logger.debug(`检测到滚动条宽度: ${this.scrollbarWidth}px`);

    // 保存原始 body 样式
    this.originalBodyStyle = document.body.style.cssText;

    // 开始监听 DOM 变化
    this.startObserving();

    // 检查当前是否已有弹窗
    this.checkExistingModals();

    this.logger.info('滚动条修复器初始化完成');
  }

  /**
   * 开始监听 DOM 变化
   */
  private startObserving(): void {
    this.observer = new MutationObserver((mutations) => {
      let shouldCheck = false;

      for (const mutation of mutations) {
        // 检查属性变化（如 style 属性）
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          if (this.isModalElement(target) && mutation.attributeName === 'style') {
            shouldCheck = true;
            break;
          }
        }
        
        // 检查节点添加/删除
        if (mutation.type === 'childList') {
          for (const node of [...mutation.addedNodes, ...mutation.removedNodes]) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (this.isModalElement(element) || this.containsModalElement(element)) {
                shouldCheck = true;
                break;
              }
            }
          }
        }

        if (shouldCheck) break;
      }

      if (shouldCheck) {
        // 使用 requestAnimationFrame 确保在 DOM 更新后执行
        requestAnimationFrame(() => {
          this.checkModalState();
        });
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  /**
   * 检查现有的弹窗
   */
  private checkExistingModals(): void {
    this.checkModalState();
  }

  /**
   * 检查弹窗状态
   */
  private checkModalState(): void {
    const hasVisibleModal = this.hasVisibleModal();
    
    if (hasVisibleModal && !this.isModalOpen) {
      this.onModalOpen();
    } else if (!hasVisibleModal && this.isModalOpen) {
      this.onModalClose();
    }
  }

  /**
   * 检查是否有可见的弹窗
   */
  private hasVisibleModal(): boolean {
    for (const selector of this.modalSelectors) {
      const modals = document.querySelectorAll(selector);
      for (const modal of modals) {
        if (this.isModalVisible(modal as HTMLElement)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 检查弹窗是否可见
   */
  private isModalVisible(modal: HTMLElement): boolean {
    const style = window.getComputedStyle(modal);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           DOMUtils.isElementVisible(modal);
  }

  /**
   * 检查元素是否是弹窗元素
   */
  private isModalElement(element: Element): boolean {
    return this.modalSelectors.some(selector => 
      element.matches && element.matches(selector)
    );
  }

  /**
   * 检查元素是否包含弹窗元素
   */
  private containsModalElement(element: Element): boolean {
    return this.modalSelectors.some(selector => 
      element.querySelector && element.querySelector(selector)
    );
  }

  /**
   * 弹窗打开时的处理
   */
  private onModalOpen(): void {
    this.logger.debug('检测到弹窗打开');
    this.isModalOpen = true;
    
    // 检查是否需要添加滚动条占位
    if (this.needsScrollbarPlaceholder()) {
      this.addScrollbarPlaceholder();
    }
  }

  /**
   * 弹窗关闭时的处理
   */
  private onModalClose(): void {
    this.logger.debug('检测到弹窗关闭');
    this.isModalOpen = false;
    this.removeScrollbarPlaceholder();
  }

  /**
   * 检查是否需要添加滚动条占位
   */
  private needsScrollbarPlaceholder(): boolean {
    // 检查页面是否有滚动条
    return document.documentElement.scrollHeight > window.innerHeight;
  }

  /**
   * 添加滚动条占位
   */
  private addScrollbarPlaceholder(): void {
    if (this.scrollbarWidth > 0) {
      document.body.style.marginRight = `${this.scrollbarWidth}px`;
      document.body.style.overflow = 'hidden';
      this.logger.debug(`添加滚动条占位: ${this.scrollbarWidth}px`);
    }
  }

  /**
   * 移除滚动条占位
   */
  private removeScrollbarPlaceholder(): void {
    document.body.style.marginRight = '';
    document.body.style.overflow = '';
    this.logger.debug('移除滚动条占位');
  }

  /**
   * 销毁修复器
   */
  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    // 恢复原始样式
    document.body.style.cssText = this.originalBodyStyle;
    
    this.logger.info('滚动条修复器已销毁');
  }
}
