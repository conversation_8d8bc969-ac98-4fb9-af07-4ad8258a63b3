#!/usr/bin/env node

/**
 * 启动开发专用浏览器
 * 绕过雪球的反调试机制
 */

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

// 获取 Chrome 路径
function getChromePath() {
  const platform = os.platform();
  
  if (platform === 'win32') {
    return [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      process.env.LOCALAPPDATA + '\\Google\\Chrome\\Application\\chrome.exe'
    ].find(path => {
      try {
        require('fs').accessSync(path);
        return true;
      } catch {
        return false;
      }
    });
  } else if (platform === 'darwin') {
    return '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
  } else {
    return 'google-chrome';
  }
}

const chromePath = getChromePath();
if (!chromePath) {
  console.error('❌ 未找到 Chrome 浏览器');
  process.exit(1);
}

console.log('🚀 启动开发专用浏览器...');
console.log('📝 这个浏览器实例专门用于开发调试');
console.log('🔧 已禁用反调试检测');
console.log('');

// Chrome 启动参数
const args = [
  '--user-data-dir=' + path.join(__dirname, '../.chrome-dev-profile'),
  '--disable-web-security',
  '--disable-features=VizDisplayCompositor',
  '--disable-dev-shm-usage',
  '--no-sandbox',
  '--disable-setuid-sandbox',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding',
  '--disable-field-trial-config',
  '--disable-ipc-flooding-protection',
  '--remote-debugging-port=9222',
  'https://xueqiu.com'
];

// 启动 Chrome
const chromeProcess = spawn(chromePath, args, {
  stdio: 'inherit',
  detached: true
});

chromeProcess.on('error', (error) => {
  console.error('❌ 启动浏览器失败:', error);
});

// 不等待子进程结束
chromeProcess.unref();

console.log('✅ 开发浏览器已启动');
console.log('💡 提示：');
console.log('   - 这个浏览器实例可以正常打开开发者工具');
console.log('   - 安装 Tampermonkey 扩展');
console.log('   - 导入脚本进行测试');
console.log('   - 关闭这个终端不会影响浏览器运行');
