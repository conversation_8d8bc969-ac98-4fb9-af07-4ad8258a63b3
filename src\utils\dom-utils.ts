/**
 * DOM 操作工具类
 */
export class DOMUtils {
  /**
   * 等待元素出现
   */
  public static waitForElement(
    selector: string,
    timeout: number = 5000,
    parent: Element | Document = document
  ): Promise<Element> {
    return new Promise((resolve, reject) => {
      const element = parent.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = (node as Element).querySelector?.(selector) || 
                             (node as Element).matches?.(selector) ? node as Element : null;
              if (element) {
                observer.disconnect();
                resolve(element);
                return;
              }
            }
          }
        }
      });

      observer.observe(parent, {
        childList: true,
        subtree: true
      });

      // 超时处理
      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * 等待元素消失
   */
  public static waitForElementRemoval(
    selector: string,
    timeout: number = 5000,
    parent: Element | Document = document
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const element = parent.querySelector(selector);
      if (!element) {
        resolve();
        return;
      }

      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          for (const node of mutation.removedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if ((node as Element).matches?.(selector) || 
                  (node as Element).querySelector?.(selector)) {
                observer.disconnect();
                resolve();
                return;
              }
            }
          }
        }
      });

      observer.observe(parent, {
        childList: true,
        subtree: true
      });

      // 超时处理
      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} removal timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * 添加样式
   */
  public static addStyle(css: string, id?: string): HTMLStyleElement {
    const style = document.createElement('style');
    style.textContent = css;
    if (id) {
      style.id = id;
    }
    document.head.appendChild(style);
    return style;
  }

  /**
   * 移除样式
   */
  public static removeStyle(id: string): void {
    const style = document.getElementById(id);
    if (style) {
      style.remove();
    }
  }

  /**
   * 检查元素是否可见
   */
  public static isElementVisible(element: Element): boolean {
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  }

  /**
   * 获取滚动条宽度
   */
  public static getScrollbarWidth(): number {
    const outer = document.createElement('div');
    outer.style.visibility = 'hidden';
    outer.style.overflow = 'scroll';
    (outer.style as any).msOverflowStyle = 'scrollbar';
    document.body.appendChild(outer);

    const inner = document.createElement('div');
    outer.appendChild(inner);

    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
    outer.remove();

    return scrollbarWidth;
  }
}
